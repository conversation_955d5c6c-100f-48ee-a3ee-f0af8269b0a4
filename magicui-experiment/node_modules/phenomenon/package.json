{"name": "phenomenon", "version": "1.6.0", "description": "A fast 2kB low-level WebGL API.", "source": "src/index.ts", "main": "dist/phenomenon.mjs", "unpkg": "dist/phenomenon.umd.js", "types": "dist/index.d.ts", "scripts": {"start": "http-server demo --silent & $npm_execpath run watch", "watch": "microbundle watch --format umd --entry demo/src/index.js --output demo/dist/bundle.js", "build": "microbundle --name Phenomenon --format es,umd --sourcemap false", "test": "npm run build && karmatic", "prepare": "$npm_execpath run test"}, "repository": {"type": "git", "url": "git+https://github.com/vaneenige/phenomenon.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://use-the-platform.com"}, "files": ["src", "dist"], "keywords": ["webgl", "particles", "shaders"], "prettier": {"printWidth": 100, "singleQuote": true, "trailingComma": "es5"}, "devDependencies": {"http-server": "^0.11.1", "karmatic": "^1.3.1", "microbundle": "^0.11.0", "webpack": "^4.31.0"}}