{"version": 3, "sources": ["../../phenomenon/dist/phenomenon.mjs", "../../cobe/dist/index.esm.js"], "sourcesContent": ["var t=[\"x\",\"y\",\"z\"],e=function(t){Object.assign(this,{uniforms:{},geometry:{vertices:[{x:0,y:0,z:0}]},mode:0,modifiers:{},attributes:[],multiplier:1,buffers:[]}),Object.assign(this,t),this.prepareProgram(),this.prepareUniforms(),this.prepareAttributes()};e.prototype.compileShader=function(t,e){var i=this.gl.createShader(t);return this.gl.shaderSource(i,e),this.gl.compileShader(i),i},e.prototype.prepareProgram=function(){var t=this.gl,e=this.vertex,i=this.fragment,r=t.createProgram();t.attachShader(r,this.compileShader(35633,e)),t.attachShader(r,this.compileShader(35632,i)),t.linkProgram(r),t.useProgram(r),this.program=r},e.prototype.prepareUniforms=function(){for(var t=Object.keys(this.uniforms),e=0;e<t.length;e+=1){var i=this.gl.getUniformLocation(this.program,t[e]);this.uniforms[t[e]].location=i}},e.prototype.prepareAttributes=function(){void 0!==this.geometry.vertices&&this.attributes.push({name:\"aPosition\",size:3}),void 0!==this.geometry.normal&&this.attributes.push({name:\"aNormal\",size:3}),this.attributeKeys=[];for(var t=0;t<this.attributes.length;t+=1)this.attributeKeys.push(this.attributes[t].name),this.prepareAttribute(this.attributes[t])},e.prototype.prepareAttribute=function(e){for(var i=this.geometry,r=this.multiplier,s=i.vertices,n=i.normal,a=new Float32Array(r*s.length*e.size),o=0;o<r;o+=1)for(var h=e.data&&e.data(o,r),u=o*s.length*e.size,f=0;f<s.length;f+=1)for(var c=0;c<e.size;c+=1){var l=this.modifiers[e.name];a[u]=void 0!==l?l(h,f,c,this):\"aPosition\"===e.name?s[f][t[c]]:\"aNormal\"===e.name?n[f][t[c]]:h[c],u+=1}this.attributes[this.attributeKeys.indexOf(e.name)].data=a,this.prepareBuffer(this.attributes[this.attributeKeys.indexOf(e.name)])},e.prototype.prepareBuffer=function(t){var e=t.data,i=t.name,r=t.size,s=this.gl.createBuffer();this.gl.bindBuffer(34962,s),this.gl.bufferData(34962,e,35044);var n=this.gl.getAttribLocation(this.program,i);this.gl.enableVertexAttribArray(n),this.gl.vertexAttribPointer(n,r,5126,!1,0,0),this.buffers[this.attributeKeys.indexOf(t.name)]={buffer:s,location:n,size:r}},e.prototype.render=function(t){var e=this,i=this.uniforms,r=this.multiplier,s=this.gl;s.useProgram(this.program);for(var n=0;n<this.buffers.length;n+=1){var a=this.buffers[n],o=a.location,h=a.buffer,u=a.size;s.enableVertexAttribArray(o),s.bindBuffer(34962,h),s.vertexAttribPointer(o,u,5126,!1,0,0)}Object.keys(t).forEach(function(e){i[e].value=t[e].value}),Object.keys(i).forEach(function(t){var r=i[t];e.uniformMap[r.type](r.location,r.value)}),s.drawArrays(this.mode,0,r*this.geometry.vertices.length),this.onRender&&this.onRender(this)},e.prototype.destroy=function(){for(var t=0;t<this.buffers.length;t+=1)this.gl.deleteBuffer(this.buffers[t].buffer);this.gl.deleteProgram(this.program),this.gl=null};var i=function(t){var e=this,i=t||{},r=i.canvas;void 0===r&&(r=document.querySelector(\"canvas\"));var s=i.context;void 0===s&&(s={});var n=i.contextType;void 0===n&&(n=\"experimental-webgl\");var a=i.settings;void 0===a&&(a={});var o=r.getContext(n,Object.assign({alpha:!1,antialias:!1},s));Object.assign(this,{gl:o,canvas:r,uniforms:{},instances:new Map,shouldRender:!0}),Object.assign(this,{devicePixelRatio:1,clearColor:[1,1,1,1],position:{x:0,y:0,z:2},clip:[.001,100]}),Object.assign(this,a),this.uniformMap={float:function(t,e){return o.uniform1f(t,e)},vec2:function(t,e){return o.uniform2fv(t,e)},vec3:function(t,e){return o.uniform3fv(t,e)},vec4:function(t,e){return o.uniform4fv(t,e)},mat2:function(t,e){return o.uniformMatrix2fv(t,!1,e)},mat3:function(t,e){return o.uniformMatrix3fv(t,!1,e)},mat4:function(t,e){return o.uniformMatrix4fv(t,!1,e)}},o.enable(o.DEPTH_TEST),o.depthFunc(o.LEQUAL),!1===o.getContextAttributes().alpha&&(o.clearColor.apply(o,this.clearColor),o.clearDepth(1)),this.onSetup&&this.onSetup(o),window.addEventListener(\"resize\",function(){return e.resize()}),this.resize(),this.render()};i.prototype.resize=function(){var t=this.gl,e=this.canvas,i=this.devicePixelRatio,r=this.position;e.width=e.clientWidth*i,e.height=e.clientHeight*i;var s=t.drawingBufferWidth,n=t.drawingBufferHeight,a=s/n;t.viewport(0,0,s,n);var o=Math.tan(Math.PI/180*22.5),h=[1,0,0,0,0,1,0,0,0,0,1,0,r.x,r.y,(a<1?1:a)*-r.z,1];this.uniforms.uProjectionMatrix={type:\"mat4\",value:[.5/o,0,0,0,0,a/o*.5,0,0,0,0,-(this.clip[1]+this.clip[0])/(this.clip[1]-this.clip[0]),-1,0,0,-2*this.clip[1]*(this.clip[0]/(this.clip[1]-this.clip[0])),0]},this.uniforms.uViewMatrix={type:\"mat4\",value:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]},this.uniforms.uModelMatrix={type:\"mat4\",value:h}},i.prototype.toggle=function(t){t!==this.shouldRender&&(this.shouldRender=void 0!==t?t:!this.shouldRender,this.shouldRender&&this.render())},i.prototype.render=function(){var t=this;this.gl.clear(16640),this.instances.forEach(function(e){e.render(t.uniforms)}),this.onRender&&this.onRender(this),this.shouldRender&&requestAnimationFrame(function(){return t.render()})},i.prototype.add=function(t,i){void 0===i&&(i={uniforms:{}}),void 0===i.uniforms&&(i.uniforms={}),Object.assign(i.uniforms,JSON.parse(JSON.stringify(this.uniforms))),Object.assign(i,{gl:this.gl,uniformMap:this.uniformMap});var r=new e(i);return this.instances.set(t,r),r},i.prototype.remove=function(t){var e=this.instances.get(t);void 0!==e&&(e.destroy(),this.instances.delete(t))},i.prototype.destroy=function(){var t=this;this.instances.forEach(function(e,i){e.destroy(),t.instances.delete(i)}),this.toggle(!1)};export default i;\n", "import B from\"phenomenon\";var M=\"phi\",R=\"theta\",c=\"mapSamples\",O=\"mapBrightness\",N=\"baseColor\",G=\"markerColor\",s=\"glowColor\",S=\"markers\",P=\"diffuse\",X=\"devicePixelRatio\",f=\"dark\",u=\"offset\",m=\"scale\",x=\"opacity\",l=\"mapBaseBrightness\",I={[M]:\"A\",[R]:\"B\",[c]:\"l\",[O]:\"E\",[N]:\"R\",[G]:\"S\",[s]:\"y\",[P]:\"F\",[f]:\"G\",[u]:\"x\",[m]:\"C\",[x]:\"H\",[l]:\"I\"},{PI:i,sin:d,cos:U}=Math,C=r=>[].concat(...r.map(E=>{let[_,o]=E.location;_=_*i/180,o=o*i/180-i;let a=U(_);return[-a*U(o),d(_),a*d(o),E.size]}),[0,0,0,0]),p=(r,E)=>{let _=(e,t,L)=>({type:e,value:typeof E[t]==\"undefined\"?L:E[t]}),o=r.getContext(\"webgl\")?\"webgl\":\"experimental-webgl\",a=new B({canvas:r,contextType:o,context:{alpha:!0,stencil:!1,antialias:!0,depth:!1,preserveDrawingBuffer:!1,...E.context},settings:{[X]:E[X]||1,onSetup:e=>{let t=e.RGB,L=e.UNSIGNED_BYTE,n=e.TEXTURE_2D,T=e.createTexture();e.bindTexture(n,T),e.texImage2D(n,0,t,1,1,0,t,L,new Uint8Array([0,0,0,0]));let A=new Image;A.onload=()=>{e.bindTexture(n,T),e.texImage2D(n,0,t,t,L,A),e.generateMipmap(n);let h=e.getParameter(e.CURRENT_PROGRAM),v=e.getUniformLocation(h,\"J\");e.texParameteri(n,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(n,e.TEXTURE_MAG_FILTER,e.NEAREST),e.uniform1i(v,0)},A.src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAACAAQAAAADMzoqnAAAAAXNSR0IArs4c6QAABA5JREFUeNrV179uHEUAx/Hf3JpbF+E2VASBsmVKTBcpKJs3SMEDcDwBiVJAAewYEBUivIHT0uUBIt0YCovKD0CRjUC4QfHYh8hYXu+P25vZ2Zm9c66gMd/GJ/tz82d3bk8GN4SrByYF2366FNTACIAkivVAAazQdnf3MvAlbNUQfOPAdQDvSAimMWhwy4I2g4SU+Kp04ISLpPBAKLxPyic3O/CCi+Y7rUJbiodcpDOFY7CgxCEXmdYD2EYK2s5lApOx5pEDDYCUwM1XdJUwBV11QQMg59kePSCaPAASQMEL2hwo6TJFgxpg+TgC2ymXPbuvc40awr3D1QCFfbH9kcoqAOkZozpQo0aqAGQRKCog/+tjkgbNFEtg2FffBvBGlSxHoAaAa1u6X4PBAwDiR8FFsrQgeUhfJTSALaB9jy5NCybJPn1SVFiWk7ywN+KzhH1aKAuydhGkbEF4lWohLXDXavlyFgHY7LBnLRdlAP6BS5Cc8RfVDXbkwN/oIvmY+6obbNeBP0JwTuMGu9gTzy1Q4RS/cWpfzszeYwd+CAFrtBW/Hur0gLbJGlD+/OjVwe/drfBxkbbg63dndEDfiEBlAd7ac0BPe1D6Jd8dfbLH+RI0OzseFB5s01/M+gMdAeluLOCAuaUA9Lezo/vSgXoCX9rtEiXnp7Q1W/CNyWcd8DXoS6jH/YZ5vAJEWY2dXFQe2TUgaFaNejCzJ98g6HnlVrsE58sDcYqg+9XY75fPqdoh/kRQWiXKg8MWlJQxUFMPjqnyujhFBE7UxIMjyszk0QwQlFsezImsyvUYYYVED2pk6m0Tg8T04Fwjk2kdAwSACqlM6gRRt3vQYAFGX0Ah7Ebx1H+MDRI5ui0QldH4j7FGcm90XdxD2Jg1AOEAVAKhEFXSn4cKUELurIAKwJ3MArypPscQaLhJFICJ0ohjDySAdH8AhDtCiTuMycH8CXzhH9jUACAO5uMhoAwA5i+T6WAKmmAqnLy80wxHqIPFYpqCwxGaYLt4Dyievg5kEoVEUAhs6pqKgFtDQYOuaXypaWKQfIuwwoGSZgfLsu/XAtI8cGN+h7Cc1A5oLOMhwlIPXuhu48AIvsSBkvtV9wsJRKCyYLfq5lTrQMFd1a262oqBck9K1V0YjQg0iEYYgpS1A9GlXQV5cykwm4A7BzVsxQqo7E+zCegO7Ma7yKgsuOcfKbMBwLC8wvVNYDsANYalEpOAa6zpWjTeMKGwEwC1CiQewJc5EKfgy7GmRAZA4vUVGwE2dPM/g0xuAInE/yG5aZ8ISxWGfYigUVbdyBElTHh2uCwGdfCkOLGgQVBh3Ewp+/QK4CDlR5Ws/Zf7yhCf8pH7vinWAvoVCQ6zz0NX5V/6GkAVV+2/5qsJ/gU8bsxpM8IeAQAAAABJRU5ErkJggg==\"}}});return a.add(\"\",{vertex:\"attribute vec3 aPosition;uniform mat4 uProjectionMatrix;uniform mat4 uModelMatrix;uniform mat4 uViewMatrix;void main(){gl_Position=uProjectionMatrix*uModelMatrix*uViewMatrix*vec4(aPosition,1.);}\",fragment:\"precision highp float;uniform vec2 t,x;uniform vec3 R,S,y;uniform vec4 z[64];uniform float A,B,l,C,D,E,F,G,H,I;uniform sampler2D J;float K=1./l;mat3 L(float a,float b){float c=cos(a),d=cos(b),e=sin(a),f=sin(b);return mat3(d,f*e,-f*c,0.,c,e,f,d*-e,d*c);}vec3 w(vec3 c,out float v){c=c.xzy;float p=max(2.,floor(log2(2.236068*l*3.141593*(1.-c.z*c.z))*.72021));vec2 g=floor(pow(1.618034,p)/2.236068*vec2(1.,1.618034)+.5),d=fract((g+1.)*.618034)*6.283185-3.883222,e=-2.*g,f=vec2(atan(c.y,c.x),c.z-1.),q=floor(vec2(e.y*f.x-d.y*(f.y*l+1.),-e.x*f.x+d.x*(f.y*l+1.))/(d.x*e.y-e.x*d.y));float n=3.141593;vec3 r;for(float h=0.;h<4.;h+=1.){vec2 s=vec2(mod(h,2.),floor(h*.5));float j=dot(g,q+s);if(j>l)continue;float a=j,b=0.;if(a>=524288.)a-=524288.,b+=.803894;if(a>=262144.)a-=262144.,b+=.901947;if(a>=131072.)a-=131072.,b+=.950973;if(a>=65536.)a-=65536.,b+=.475487;if(a>=32768.)a-=32768.,b+=.737743;if(a>=16384.)a-=16384.,b+=.868872;if(a>=8192.)a-=8192.,b+=.934436;if(a>=4096.)a-=4096.,b+=.467218;if(a>=2048.)a-=2048.,b+=.733609;if(a>=1024.)a-=1024.,b+=.866804;if(a>=512.)a-=512.,b+=.433402;if(a>=256.)a-=256.,b+=.216701;if(a>=128.)a-=128.,b+=.108351;if(a>=64.)a-=64.,b+=.554175;if(a>=32.)a-=32.,b+=.777088;if(a>=16.)a-=16.,b+=.888544;if(a>=8.)a-=8.,b+=.944272;if(a>=4.)a-=4.,b+=.472136;if(a>=2.)a-=2.,b+=.236068;if(a>=1.)a-=1.,b+=.618034;float k=fract(b)*6.283185,i=1.-2.*j*K,m=sqrt(1.-i*i);vec3 o=vec3(cos(k)*m,sin(k)*m,i);float u=length(c-o);if(u<n)n=u,r=o;}v=n;return r.xzy;}void main(){vec2 b=(gl_FragCoord.xy/t*2.-1.)/C-x*vec2(1.,-1.)/t;b.x*=t.x/t.y;float c=dot(b,b);vec4 M=vec4(0.);float m=0.;if(c<=.64){for(int d=0;d<2;d++){vec4 e=vec4(0.);float a;vec3 u=vec3(0.,0.,1.),f=normalize(vec3(b,sqrt(.64-c)));f.z*=d>0?-1.:1.,u.z*=d>0?-1.:1.;vec3 g=f*L(B,A),h=w(g,a);float n=asin(h.y),i=acos(-h.x/cos(n));i=h.z<0.?-i:i;float N=max(texture2D(J,vec2(i*.5/3.141593,-(n/3.141593+.5))).x,I),O=smoothstep(8e-3,0.,a),j=dot(f,u),v=pow(j,F)*E,o=N*O*v,T=mix((1.-o)*pow(j,.4),o,G)+.1;e+=vec4(R*T,1.);int U=int(D);float p=0.;for(int k=0;k<64;k++){if(k>=U)break;vec4 q=z[k];vec3 r=q.xyz,P=r-g;float s=q.w;if(dot(P,P)>s*s*4.)continue;vec3 V=w(r,a);a=length(V-g),a<s?p+=smoothstep(s*.5,0.,a):0.;}p=min(1.,p*v),e.xyz=mix(e.xyz,S,p),e.xyz+=pow(1.-j,4.)*y,M+=e*(1.+(d>0?-H:H))/2.;}m=pow(dot(normalize(vec3(-b,sqrt(1.-c))),vec3(0.,0.,1.)),4.)*smoothstep(0.,1.,.2/(c-.64));}else{float Q=sqrt(.2/(c-.64));m=smoothstep(.5,1.,Q/(Q+1.));}gl_FragColor=M+vec4(m*y,m);}\",uniforms:{t:{type:\"vec2\",value:[E.width,E.height]},A:_(\"float\",M),B:_(\"float\",R),l:_(\"float\",c),E:_(\"float\",O),I:_(\"float\",l),R:_(\"vec3\",N),S:_(\"vec3\",G),F:_(\"float\",P),y:_(\"vec3\",s),G:_(\"float\",f),z:{type:\"vec4\",value:C(E[S])},D:{type:\"float\",value:E[S].length},x:_(\"vec2\",u,[0,0]),C:_(\"float\",m,1),H:_(\"float\",x,1)},mode:4,geometry:{vertices:[{x:-100,y:100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0},{x:100,y:-100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0}]},onRender:({uniforms:e})=>{let t={};if(E.onRender){t=E.onRender(t)||t;for(let L in I)t[L]!==void 0&&(e[I[L]].value=t[L]);t[S]!==void 0&&(e[\"z\"].value=C(t[S]),e[\"D\"].value=t[S].length),t.width&&t.height&&(e[\"t\"].value=[t.width,t.height])}}}),a};export{p as default};\n"], "mappings": ";;;AAAA,IAAI,IAAE,CAAC,KAAI,KAAI,GAAG;AAAlB,IAAoB,IAAE,SAASA,IAAE;AAAC,SAAO,OAAO,MAAK,EAAC,UAAS,CAAC,GAAE,UAAS,EAAC,UAAS,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,CAAC,EAAC,GAAE,MAAK,GAAE,WAAU,CAAC,GAAE,YAAW,CAAC,GAAE,YAAW,GAAE,SAAQ,CAAC,EAAC,CAAC,GAAE,OAAO,OAAO,MAAKA,EAAC,GAAE,KAAK,eAAe,GAAE,KAAK,gBAAgB,GAAE,KAAK,kBAAkB;AAAC;AAAE,EAAE,UAAU,gBAAc,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,GAAG,aAAaF,EAAC;AAAE,SAAO,KAAK,GAAG,aAAaE,IAAED,EAAC,GAAE,KAAK,GAAG,cAAcC,EAAC,GAAEA;AAAC,GAAE,EAAE,UAAU,iBAAe,WAAU;AAAC,MAAIF,KAAE,KAAK,IAAGC,KAAE,KAAK,QAAOC,KAAE,KAAK,UAAS,IAAEF,GAAE,cAAc;AAAE,EAAAA,GAAE,aAAa,GAAE,KAAK,cAAc,OAAMC,EAAC,CAAC,GAAED,GAAE,aAAa,GAAE,KAAK,cAAc,OAAME,EAAC,CAAC,GAAEF,GAAE,YAAY,CAAC,GAAEA,GAAE,WAAW,CAAC,GAAE,KAAK,UAAQ;AAAC,GAAE,EAAE,UAAU,kBAAgB,WAAU;AAAC,WAAQA,KAAE,OAAO,KAAK,KAAK,QAAQ,GAAEC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAG,GAAE;AAAC,QAAIC,KAAE,KAAK,GAAG,mBAAmB,KAAK,SAAQF,GAAEC,EAAC,CAAC;AAAE,SAAK,SAASD,GAAEC,EAAC,CAAC,EAAE,WAASC;AAAA,EAAC;AAAC,GAAE,EAAE,UAAU,oBAAkB,WAAU;AAAC,aAAS,KAAK,SAAS,YAAU,KAAK,WAAW,KAAK,EAAC,MAAK,aAAY,MAAK,EAAC,CAAC,GAAE,WAAS,KAAK,SAAS,UAAQ,KAAK,WAAW,KAAK,EAAC,MAAK,WAAU,MAAK,EAAC,CAAC,GAAE,KAAK,gBAAc,CAAC;AAAE,WAAQF,KAAE,GAAEA,KAAE,KAAK,WAAW,QAAOA,MAAG;AAAE,SAAK,cAAc,KAAK,KAAK,WAAWA,EAAC,EAAE,IAAI,GAAE,KAAK,iBAAiB,KAAK,WAAWA,EAAC,CAAC;AAAC,GAAE,EAAE,UAAU,mBAAiB,SAASC,IAAE;AAAC,WAAQC,KAAE,KAAK,UAAS,IAAE,KAAK,YAAWC,KAAED,GAAE,UAAS,IAAEA,GAAE,QAAO,IAAE,IAAI,aAAa,IAAEC,GAAE,SAAOF,GAAE,IAAI,GAAE,IAAE,GAAE,IAAE,GAAE,KAAG;AAAE,aAAQ,IAAEA,GAAE,QAAMA,GAAE,KAAK,GAAE,CAAC,GAAEG,KAAE,IAAED,GAAE,SAAOF,GAAE,MAAKI,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG;AAAE,eAAQC,KAAE,GAAEA,KAAEL,GAAE,MAAKK,MAAG,GAAE;AAAC,YAAIC,KAAE,KAAK,UAAUN,GAAE,IAAI;AAAE,UAAEG,EAAC,IAAE,WAASG,KAAEA,GAAE,GAAEF,IAAEC,IAAE,IAAI,IAAE,gBAAcL,GAAE,OAAKE,GAAEE,EAAC,EAAE,EAAEC,EAAC,CAAC,IAAE,cAAYL,GAAE,OAAK,EAAEI,EAAC,EAAE,EAAEC,EAAC,CAAC,IAAE,EAAEA,EAAC,GAAEF,MAAG;AAAA,MAAC;AAAC,OAAK,WAAW,KAAK,cAAc,QAAQH,GAAE,IAAI,CAAC,EAAE,OAAK,GAAE,KAAK,cAAc,KAAK,WAAW,KAAK,cAAc,QAAQA,GAAE,IAAI,CAAC,CAAC;AAAC,GAAE,EAAE,UAAU,gBAAc,SAASD,IAAE;AAAC,MAAIC,KAAED,GAAE,MAAKE,KAAEF,GAAE,MAAK,IAAEA,GAAE,MAAKG,KAAE,KAAK,GAAG,aAAa;AAAE,OAAK,GAAG,WAAW,OAAMA,EAAC,GAAE,KAAK,GAAG,WAAW,OAAMF,IAAE,KAAK;AAAE,MAAI,IAAE,KAAK,GAAG,kBAAkB,KAAK,SAAQC,EAAC;AAAE,OAAK,GAAG,wBAAwB,CAAC,GAAE,KAAK,GAAG,oBAAoB,GAAE,GAAE,MAAK,OAAG,GAAE,CAAC,GAAE,KAAK,QAAQ,KAAK,cAAc,QAAQF,GAAE,IAAI,CAAC,IAAE,EAAC,QAAOG,IAAE,UAAS,GAAE,MAAK,EAAC;AAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,MAAIC,KAAE,MAAKC,KAAE,KAAK,UAAS,IAAE,KAAK,YAAWC,KAAE,KAAK;AAAG,EAAAA,GAAE,WAAW,KAAK,OAAO;AAAE,WAAQ,IAAE,GAAE,IAAE,KAAK,QAAQ,QAAO,KAAG,GAAE;AAAC,QAAI,IAAE,KAAK,QAAQ,CAAC,GAAE,IAAE,EAAE,UAAS,IAAE,EAAE,QAAOC,KAAE,EAAE;AAAK,IAAAD,GAAE,wBAAwB,CAAC,GAAEA,GAAE,WAAW,OAAM,CAAC,GAAEA,GAAE,oBAAoB,GAAEC,IAAE,MAAK,OAAG,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO,KAAKJ,EAAC,EAAE,QAAQ,SAASC,IAAE;AAAC,IAAAC,GAAED,EAAC,EAAE,QAAMD,GAAEC,EAAC,EAAE;AAAA,EAAK,CAAC,GAAE,OAAO,KAAKC,EAAC,EAAE,QAAQ,SAASF,IAAE;AAAC,QAAIQ,KAAEN,GAAEF,EAAC;AAAE,IAAAC,GAAE,WAAWO,GAAE,IAAI,EAAEA,GAAE,UAASA,GAAE,KAAK;AAAA,EAAC,CAAC,GAAEL,GAAE,WAAW,KAAK,MAAK,GAAE,IAAE,KAAK,SAAS,SAAS,MAAM,GAAE,KAAK,YAAU,KAAK,SAAS,IAAI;AAAC,GAAE,EAAE,UAAU,UAAQ,WAAU;AAAC,WAAQH,KAAE,GAAEA,KAAE,KAAK,QAAQ,QAAOA,MAAG;AAAE,SAAK,GAAG,aAAa,KAAK,QAAQA,EAAC,EAAE,MAAM;AAAE,OAAK,GAAG,cAAc,KAAK,OAAO,GAAE,KAAK,KAAG;AAAI;AAAE,IAAI,IAAE,SAASA,IAAE;AAAC,MAAIC,KAAE,MAAKC,KAAEF,MAAG,CAAC,GAAE,IAAEE,GAAE;AAAO,aAAS,MAAI,IAAE,SAAS,cAAc,QAAQ;AAAG,MAAIC,KAAED,GAAE;AAAQ,aAASC,OAAIA,KAAE,CAAC;AAAG,MAAI,IAAED,GAAE;AAAY,aAAS,MAAI,IAAE;AAAsB,MAAI,IAAEA,GAAE;AAAS,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE,WAAW,GAAE,OAAO,OAAO,EAAC,OAAM,OAAG,WAAU,MAAE,GAAEC,EAAC,CAAC;AAAE,SAAO,OAAO,MAAK,EAAC,IAAG,GAAE,QAAO,GAAE,UAAS,CAAC,GAAE,WAAU,oBAAI,OAAI,cAAa,KAAE,CAAC,GAAE,OAAO,OAAO,MAAK,EAAC,kBAAiB,GAAE,YAAW,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,UAAS,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,CAAC,MAAK,GAAG,EAAC,CAAC,GAAE,OAAO,OAAO,MAAK,CAAC,GAAE,KAAK,aAAW,EAAC,OAAM,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,UAAUD,IAAEC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,WAAWD,IAAEC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,WAAWD,IAAEC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,WAAWD,IAAEC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,iBAAiBD,IAAE,OAAGC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,iBAAiBD,IAAE,OAAGC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,iBAAiBD,IAAE,OAAGC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAE,OAAO,EAAE,UAAU,GAAE,EAAE,UAAU,EAAE,MAAM,GAAE,UAAK,EAAE,qBAAqB,EAAE,UAAQ,EAAE,WAAW,MAAM,GAAE,KAAK,UAAU,GAAE,EAAE,WAAW,CAAC,IAAG,KAAK,WAAS,KAAK,QAAQ,CAAC,GAAE,OAAO,iBAAiB,UAAS,WAAU;AAAC,WAAOA,GAAE,OAAO;AAAA,EAAC,CAAC,GAAE,KAAK,OAAO,GAAE,KAAK,OAAO;AAAC;AAAE,EAAE,UAAU,SAAO,WAAU;AAAC,MAAID,KAAE,KAAK,IAAGC,KAAE,KAAK,QAAOC,KAAE,KAAK,kBAAiB,IAAE,KAAK;AAAS,EAAAD,GAAE,QAAMA,GAAE,cAAYC,IAAED,GAAE,SAAOA,GAAE,eAAaC;AAAE,MAAIC,KAAEH,GAAE,oBAAmB,IAAEA,GAAE,qBAAoB,IAAEG,KAAE;AAAE,EAAAH,GAAE,SAAS,GAAE,GAAEG,IAAE,CAAC;AAAE,MAAI,IAAE,KAAK,IAAI,KAAK,KAAG,MAAI,IAAI,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE,EAAE,IAAG,IAAE,IAAE,IAAE,KAAG,CAAC,EAAE,GAAE,CAAC;AAAE,OAAK,SAAS,oBAAkB,EAAC,MAAK,QAAO,OAAM,CAAC,MAAG,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAE,KAAG,GAAE,GAAE,GAAE,GAAE,EAAE,KAAK,KAAK,CAAC,IAAE,KAAK,KAAK,CAAC,MAAI,KAAK,KAAK,CAAC,IAAE,KAAK,KAAK,CAAC,IAAG,IAAG,GAAE,GAAE,KAAG,KAAK,KAAK,CAAC,KAAG,KAAK,KAAK,CAAC,KAAG,KAAK,KAAK,CAAC,IAAE,KAAK,KAAK,CAAC,KAAI,CAAC,EAAC,GAAE,KAAK,SAAS,cAAY,EAAC,MAAK,QAAO,OAAM,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAC,GAAE,KAAK,SAAS,eAAa,EAAC,MAAK,QAAO,OAAM,EAAC;AAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,EAAAA,OAAI,KAAK,iBAAe,KAAK,eAAa,WAASA,KAAEA,KAAE,CAAC,KAAK,cAAa,KAAK,gBAAc,KAAK,OAAO;AAAE,GAAE,EAAE,UAAU,SAAO,WAAU;AAAC,MAAIA,KAAE;AAAK,OAAK,GAAG,MAAM,KAAK,GAAE,KAAK,UAAU,QAAQ,SAASC,IAAE;AAAC,IAAAA,GAAE,OAAOD,GAAE,QAAQ;AAAA,EAAC,CAAC,GAAE,KAAK,YAAU,KAAK,SAAS,IAAI,GAAE,KAAK,gBAAc,sBAAsB,WAAU;AAAC,WAAOA,GAAE,OAAO;AAAA,EAAC,CAAC;AAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEE,IAAE;AAAC,aAASA,OAAIA,KAAE,EAAC,UAAS,CAAC,EAAC,IAAG,WAASA,GAAE,aAAWA,GAAE,WAAS,CAAC,IAAG,OAAO,OAAOA,GAAE,UAAS,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC,GAAE,OAAO,OAAOA,IAAE,EAAC,IAAG,KAAK,IAAG,YAAW,KAAK,WAAU,CAAC;AAAE,MAAI,IAAE,IAAI,EAAEA,EAAC;AAAE,SAAO,KAAK,UAAU,IAAIF,IAAE,CAAC,GAAE;AAAC,GAAE,EAAE,UAAU,SAAO,SAASA,IAAE;AAAC,MAAIC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,aAASC,OAAIA,GAAE,QAAQ,GAAE,KAAK,UAAU,OAAOD,EAAC;AAAE,GAAE,EAAE,UAAU,UAAQ,WAAU;AAAC,MAAIA,KAAE;AAAK,OAAK,UAAU,QAAQ,SAASC,IAAEC,IAAE;AAAC,IAAAD,GAAE,QAAQ,GAAED,GAAE,UAAU,OAAOE,EAAC;AAAA,EAAC,CAAC,GAAE,KAAK,OAAO,KAAE;AAAC;AAAE,IAAO,qBAAQ;;;ACAzwK,IAAI,IAAE;AAAN,IAAY,IAAE;AAAd,IAAsB,IAAE;AAAxB,IAAqC,IAAE;AAAvC,IAAuD,IAAE;AAAzD,IAAqE,IAAE;AAAvE,IAAqF,IAAE;AAAvF,IAAmG,IAAE;AAArG,IAA+G,IAAE;AAAjH,IAA2H,IAAE;AAA7H,IAAgJ,IAAE;AAAlJ,IAAyJ,IAAE;AAA3J,IAAoK,IAAE;AAAtK,IAA8K,IAAE;AAAhL,IAA0L,IAAE;AAA5L,IAAgN,IAAE,EAAC,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,GAAE,IAAG;AAA1T,IAA4T,EAAC,IAAGO,IAAE,KAAI,GAAE,KAAI,EAAC,IAAE;AAA/U,IAAoV,IAAE,OAAG,CAAC,EAAE,OAAO,GAAG,EAAE,IAAI,OAAG;AAAC,MAAG,CAAC,GAAE,CAAC,IAAE,EAAE;AAAS,MAAE,IAAEA,KAAE,KAAI,IAAE,IAAEA,KAAE,MAAIA;AAAE,MAAI,IAAE,EAAE,CAAC;AAAE,SAAM,CAAC,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,EAAE,IAAI;AAAC,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;AAAnd,IAAqd,IAAE,CAAC,GAAE,MAAI;AAAC,MAAI,IAAE,CAACC,IAAEC,IAAE,OAAK,EAAC,MAAKD,IAAE,OAAM,OAAO,EAAEC,EAAC,KAAG,cAAY,IAAE,EAAEA,EAAC,EAAC,IAAG,IAAE,EAAE,WAAW,OAAO,IAAE,UAAQ,sBAAqB,IAAE,IAAI,mBAAE,EAAC,QAAO,GAAE,aAAY,GAAE,SAAQ,EAAC,OAAM,MAAG,SAAQ,OAAG,WAAU,MAAG,OAAM,OAAG,uBAAsB,OAAG,GAAG,EAAE,QAAO,GAAE,UAAS,EAAC,CAAC,CAAC,GAAE,EAAE,CAAC,KAAG,GAAE,SAAQ,CAAAD,OAAG;AAAC,QAAIC,KAAED,GAAE,KAAI,IAAEA,GAAE,eAAc,IAAEA,GAAE,YAAW,IAAEA,GAAE,cAAc;AAAE,IAAAA,GAAE,YAAY,GAAE,CAAC,GAAEA,GAAE,WAAW,GAAE,GAAEC,IAAE,GAAE,GAAE,GAAEA,IAAE,GAAE,IAAI,WAAW,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,QAAI,IAAE,IAAI;AAAM,MAAE,SAAO,MAAI;AAAC,MAAAD,GAAE,YAAY,GAAE,CAAC,GAAEA,GAAE,WAAW,GAAE,GAAEC,IAAEA,IAAE,GAAE,CAAC,GAAED,GAAE,eAAe,CAAC;AAAE,UAAI,IAAEA,GAAE,aAAaA,GAAE,eAAe,GAAE,IAAEA,GAAE,mBAAmB,GAAE,GAAG;AAAE,MAAAA,GAAE,cAAc,GAAEA,GAAE,oBAAmBA,GAAE,OAAO,GAAEA,GAAE,cAAc,GAAEA,GAAE,oBAAmBA,GAAE,OAAO,GAAEA,GAAE,UAAU,GAAE,CAAC;AAAA,IAAC,GAAE,EAAE,MAAI;AAAA,EAAg+C,EAAC,EAAC,CAAC;AAAE,SAAO,EAAE,IAAI,IAAG,EAAC,QAAO,sMAAqM,UAAS,o4EAAm4E,UAAS,EAAC,GAAE,EAAC,MAAK,QAAO,OAAM,CAAC,EAAE,OAAM,EAAE,MAAM,EAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,QAAO,CAAC,GAAE,GAAE,EAAE,QAAO,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAE,QAAO,CAAC,GAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,GAAE,EAAC,MAAK,QAAO,OAAM,EAAE,EAAE,CAAC,CAAC,EAAC,GAAE,GAAE,EAAC,MAAK,SAAQ,OAAM,EAAE,CAAC,EAAE,OAAM,GAAE,GAAE,EAAE,QAAO,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,GAAE,EAAE,SAAQ,GAAE,CAAC,GAAE,GAAE,EAAE,SAAQ,GAAE,CAAC,EAAC,GAAE,MAAK,GAAE,UAAS,EAAC,UAAS,CAAC,EAAC,GAAE,MAAK,GAAE,KAAI,GAAE,EAAC,GAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,MAAK,GAAE,EAAC,GAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,CAAC,EAAC,GAAE,UAAS,CAAC,EAAC,UAASA,GAAC,MAAI;AAAC,QAAIC,KAAE,CAAC;AAAE,QAAG,EAAE,UAAS;AAAC,MAAAA,KAAE,EAAE,SAASA,EAAC,KAAGA;AAAE,eAAQ,KAAK;AAAE,QAAAA,GAAE,CAAC,MAAI,WAASD,GAAE,EAAE,CAAC,CAAC,EAAE,QAAMC,GAAE,CAAC;AAAG,MAAAA,GAAE,CAAC,MAAI,WAASD,GAAE,GAAG,EAAE,QAAM,EAAEC,GAAE,CAAC,CAAC,GAAED,GAAE,GAAG,EAAE,QAAMC,GAAE,CAAC,EAAE,SAAQA,GAAE,SAAOA,GAAE,WAASD,GAAE,GAAG,EAAE,QAAM,CAACC,GAAE,OAAMA,GAAE,MAAM;AAAA,IAAE;AAAA,EAAC,EAAC,CAAC,GAAE;AAAC;", "names": ["t", "e", "i", "s", "u", "f", "c", "l", "r", "i", "e", "t"]}