{"hash": "8074c587", "browserHash": "bb282e65", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "eb08b8ed", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "592dbf13", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "01fa39d7", "needsInterop": false}, "cobe": {"src": "../../cobe/dist/index.esm.js", "file": "cobe.js", "fileHash": "1832517c", "needsInterop": false}, "motion/react": {"src": "../../motion/dist/es/motion/lib/react.mjs", "file": "motion_react.js", "fileHash": "8e27eb97", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "474fac6b", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "28173f9c", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "eee72571", "needsInterop": false}}, "chunks": {"chunk-4Y3WHDJB": {"file": "chunk-4Y3WHDJB.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}